<template>
  <el-dialog
    title="试卷导出"
    :visible.sync="visible"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="exportForm" :rules="rules" ref="exportForm" label-width="120px">
      <!-- 导出类型选择 -->
      <el-form-item label="导出格式" prop="exportType">
        <el-radio-group v-model="exportForm.exportType">
          <el-radio label="PDF">PDF格式</el-radio>
          <el-radio label="WORD">Word格式</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 内容选项 -->
      <el-form-item label="导出内容">
        <el-checkbox-group v-model="contentOptions">
          <el-checkbox label="questions">试卷题目</el-checkbox>
          <el-checkbox label="answers">题目答案</el-checkbox>
          <el-checkbox label="analysis">题目解析</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 格式选项 -->
      <el-form-item label="页面设置">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-select v-model="exportForm.formatOptions.pageSize" placeholder="页面大小">
              <el-option label="A4" value="A4"></el-option>
              <el-option label="A3" value="A3"></el-option>
              <el-option label="Letter" value="Letter"></el-option>
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input-number
              v-model="exportForm.formatOptions.fontSize"
              :min="8"
              :max="24"
              label="字体大小"
            ></el-input-number>
          </el-col>
          <el-col :span="8">
            <el-input-number
              v-model="exportForm.formatOptions.margin"
              :min="10"
              :max="50"
              label="页边距(mm)"
            ></el-input-number>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 其他选项 -->
      <el-form-item label="其他选项">
        <el-checkbox v-model="exportForm.formatOptions.showQuestionNumbers">显示题目序号</el-checkbox>
        <el-checkbox v-model="exportForm.formatOptions.showScores">显示分数</el-checkbox>
      </el-form-item>

      <!-- 导出方式选择 -->
      <el-form-item label="导出方式">
        <el-radio-group v-model="exportMode">
          <el-radio label="sync">立即下载</el-radio>
          <el-radio label="async">后台生成</el-radio>
        </el-radio-group>
        <div style="color: #909399; font-size: 12px; margin-top: 5px;">
          立即下载：适合小文件，直接下载<br>
          后台生成：适合大文件，生成完成后通知下载
        </div>
      </el-form-item>
    </el-form>

    <!-- 进度显示 -->
    <div v-if="exporting" style="margin: 20px 0;">
      <el-progress
        :percentage="exportProgress"
        :status="exportStatus === 'failed' ? 'exception' : 'success'"
      ></el-progress>
      <div style="text-align: center; margin-top: 10px; color: #606266;">
        {{ exportMessage }}
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        @click="handleExport"
        :loading="exporting"
        :disabled="contentOptions.length === 0"
      >
        {{ exporting ? '导出中...' : '开始导出' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PaperExportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paperId: {
      type: String,
      required: true
    },
    paperFlag: {
      type: String,
      default: 'ZJ'
    }
  },
  data() {
    return {
      exportForm: {
        exportType: 'PDF',
        formatOptions: {
          pageSize: 'A4',
          fontSize: 12,
          margin: 20,
          lineSpacing: 1.5,
          showQuestionNumbers: true,
          showScores: true
        }
      },
      contentOptions: ['questions'],
      exportMode: 'sync',
      exporting: false,
      exportProgress: 0,
      exportStatus: '',
      exportMessage: '',
      currentTaskId: null,
      rules: {
        exportType: [
          { required: true, message: '请选择导出格式', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    handleExport() {
      this.$refs.exportForm.validate(async (valid) => {
        if (!valid) return;

        if (this.contentOptions.length === 0) {
          this.$message.warning('请至少选择一项导出内容');
          return;
        }

        this.exporting = true;
        this.exportProgress = 0;
        this.exportMessage = '准备导出...';

        try {
          const exportData = this.buildExportData();

          if (this.exportMode === 'sync') {
            await this.syncExport(exportData);
          } else {
            await this.asyncExport(exportData);
          }
        } catch (error) {
          this.$message.error('导出失败: ' + error.message);
          this.exporting = false;
        }
      });
    },

    buildExportData() {
      return {
        paperId: this.paperId,
        exportType: this.exportForm.exportType,
        contentOptions: {
          includeQuestions: this.contentOptions.includes('questions'),
          includeAnswers: this.contentOptions.includes('answers'),
          includeAnalysis: this.contentOptions.includes('analysis')
        },
        formatOptions: this.exportForm.formatOptions,
        flag: this.paperFlag
      };
    },

    async syncExport(exportData) {
      try {
        const url = exportData.exportType === 'PDF' 
          ? '/qh/paper/export/pdf' 
          : '/qh/paper/export/word';

        // 使用fetch下载文件
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exportData)
        });

        if (response.ok) {
          // 获取文件名
          const contentDisposition = response.headers.get('Content-Disposition');
          let filename = 'export.' + (exportData.exportType === 'PDF' ? 'pdf' : 'docx');
          if (contentDisposition) {
            const matches = contentDisposition.match(/filename=(.+)/);
            if (matches) {
              filename = decodeURIComponent(matches[1]);
            }
          }

          // 下载文件
          const blob = await response.blob();
          const downloadUrl = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = downloadUrl;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(downloadUrl);

          this.$message.success('导出成功');
          this.handleClose();
        } else {
          throw new Error('导出失败');
        }
      } finally {
        this.exporting = false;
      }
    },

    async asyncExport(exportData) {
      try {
        // 提交异步导出任务
        const response = await this.$http.post('/qh/paper/export/async', exportData);
        this.currentTaskId = response.data;
        this.exportMessage = '任务已提交，正在生成...';

        // 开始轮询状态
        this.pollExportStatus();
      } catch (error) {
        this.exporting = false;
        throw error;
      }
    },

    async pollExportStatus() {
      if (!this.currentTaskId) return;

      try {
        const response = await this.$http.get(`/qh/paper/export/status/${this.currentTaskId}`);
        const status = response.data;

        this.exportProgress = status.progress || 0;
        this.exportMessage = status.message || '处理中...';
        this.exportStatus = status.status;

        if (status.status === 'completed') {
          this.exportMessage = '导出完成，开始下载...';
          this.downloadFile();
        } else if (status.status === 'failed') {
          throw new Error(status.message || '导出失败');
        } else {
          // 继续轮询
          setTimeout(() => this.pollExportStatus(), 2000);
        }
      } catch (error) {
        this.exporting = false;
        this.exportStatus = 'failed';
        throw error;
      }
    },

    downloadFile() {
      if (!this.currentTaskId) return;

      window.open(`/qh/paper/export/download/${this.currentTaskId}`, '_blank');
      this.$message.success('导出完成');
      this.handleClose();
    },

    handleClose() {
      this.$emit('update:visible', false);
      this.resetForm();
    },

    resetForm() {
      this.exporting = false;
      this.exportProgress = 0;
      this.exportMessage = '';
      this.exportStatus = '';
      this.currentTaskId = null;
      this.contentOptions = ['questions'];
      this.exportMode = 'sync';
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
