/**
 * 试卷导出功能前端API调用示例
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */

// 导出试卷为PDF（同步）
function exportPaperToPdf(paperId, options) {
    const exportData = {
        paperId: paperId,
        exportType: 'PDF',
        contentOptions: {
            includeQuestions: options.includeQuestions || true,
            includeAnswers: options.includeAnswers || false,
            includeAnalysis: options.includeAnalysis || false
        },
        formatOptions: {
            pageSize: options.pageSize || 'A4',
            fontSize: options.fontSize || 12,
            showQuestionNumbers: options.showQuestionNumbers !== false,
            showScores: options.showScores !== false,
            margin: options.margin || 20,
            lineSpacing: options.lineSpacing || 1.5
        },
        flag: options.flag || 'ZJ'
    };

    // 创建表单提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/qh/paper/export/pdf';
    form.style.display = 'none';

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'exportData';
    input.value = JSON.stringify(exportData);
    form.appendChild(input);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 导出试卷为Word（同步）
function exportPaperToWord(paperId, options) {
    const exportData = {
        paperId: paperId,
        exportType: 'WORD',
        contentOptions: {
            includeQuestions: options.includeQuestions || true,
            includeAnswers: options.includeAnswers || false,
            includeAnalysis: options.includeAnalysis || false
        },
        formatOptions: {
            pageSize: options.pageSize || 'A4',
            fontSize: options.fontSize || 12,
            showQuestionNumbers: options.showQuestionNumbers !== false,
            showScores: options.showScores !== false,
            margin: options.margin || 20,
            lineSpacing: options.lineSpacing || 1.5
        },
        flag: options.flag || 'ZJ'
    };

    // 创建表单提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/qh/paper/export/word';
    form.style.display = 'none';

    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'exportData';
    input.value = JSON.stringify(exportData);
    form.appendChild(input);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 异步导出试卷
async function exportPaperAsync(paperId, exportType, options) {
    const exportData = {
        paperId: paperId,
        exportType: exportType,
        contentOptions: {
            includeQuestions: options.includeQuestions || true,
            includeAnswers: options.includeAnswers || false,
            includeAnalysis: options.includeAnalysis || false
        },
        formatOptions: {
            pageSize: options.pageSize || 'A4',
            fontSize: options.fontSize || 12,
            showQuestionNumbers: options.showQuestionNumbers !== false,
            showScores: options.showScores !== false,
            margin: options.margin || 20,
            lineSpacing: options.lineSpacing || 1.5
        },
        flag: options.flag || 'ZJ'
    };

    try {
        const response = await fetch('/qh/paper/export/async', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(exportData)
        });

        const result = await response.json();
        if (result.code === 200) {
            return result.data; // 返回taskId
        } else {
            throw new Error(result.msg || '导出失败');
        }
    } catch (error) {
        console.error('异步导出失败:', error);
        throw error;
    }
}

// 查询导出状态
async function getExportStatus(taskId) {
    try {
        const response = await fetch(`/qh/paper/export/status/${taskId}`);
        const result = await response.json();
        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.msg || '查询状态失败');
        }
    } catch (error) {
        console.error('查询导出状态失败:', error);
        throw error;
    }
}

// 下载导出的文件
function downloadExportedFile(taskId) {
    window.open(`/qh/paper/export/download/${taskId}`, '_blank');
}

// 轮询导出状态并自动下载
async function pollExportStatusAndDownload(taskId, onProgress) {
    const pollInterval = 2000; // 2秒轮询一次
    const maxAttempts = 150; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
        try {
            attempts++;
            const status = await getExportStatus(taskId);
            
            if (onProgress && typeof onProgress === 'function') {
                onProgress(status);
            }

            if (status.status === 'completed') {
                // 导出完成，自动下载
                downloadExportedFile(taskId);
                return status;
            } else if (status.status === 'failed') {
                throw new Error(status.message || '导出失败');
            } else if (attempts >= maxAttempts) {
                throw new Error('导出超时');
            } else {
                // 继续轮询
                setTimeout(poll, pollInterval);
            }
        } catch (error) {
            console.error('轮询导出状态失败:', error);
            throw error;
        }
    };

    return poll();
}

// 使用示例
/*
// 1. 同步导出PDF（仅试卷）
exportPaperToPdf('paper123', {
    includeQuestions: true,
    includeAnswers: false,
    includeAnalysis: false
});

// 2. 同步导出Word（试卷+答案+解析）
exportPaperToWord('paper123', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: true,
    fontSize: 14
});

// 3. 异步导出PDF并轮询状态
exportPaperAsync('paper123', 'PDF', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: false
}).then(taskId => {
    console.log('导出任务已提交，任务ID:', taskId);
    
    // 轮询状态并自动下载
    pollExportStatusAndDownload(taskId, (status) => {
        console.log('导出进度:', status.progress + '%', status.message);
    }).then(() => {
        console.log('导出完成并已开始下载');
    }).catch(error => {
        console.error('导出失败:', error);
    });
}).catch(error => {
    console.error('提交导出任务失败:', error);
});
*/
