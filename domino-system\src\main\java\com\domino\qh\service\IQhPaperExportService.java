package com.domino.qh.service;

import com.domino.common.qh.dto.QhPaperExportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * 试卷导出服务接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IQhPaperExportService {

    /**
     * 导出试卷为PDF
     *
     * @param exportDTO 导出参数
     * @param response  HTTP响应
     */
    void exportToPdf(QhPaperExportDTO exportDTO, HttpServletResponse response);

    /**
     * 导出试卷为Word
     *
     * @param exportDTO 导出参数
     * @param response  HTTP响应
     */
    void exportToWord(QhPaperExportDTO exportDTO, HttpServletResponse response);

    /**
     * 异步导出试卷
     *
     * @param exportDTO 导出参数
     * @return 任务ID
     */
    String exportAsync(QhPaperExportDTO exportDTO);

    /**
     * 查询导出任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Object getExportStatus(String taskId);

    /**
     * 下载导出的文件
     *
     * @param taskId   任务ID
     * @param response HTTP响应
     */
    void downloadExportedFile(String taskId, HttpServletResponse response);
}
