# 试卷导出功能说明

## 功能概述

试卷导出功能支持将试卷导出为PDF或Word格式，可以灵活选择导出内容（试卷、答案、解析）和格式选项。支持同步导出和异步导出两种方式。

## 主要特性

- ✅ 支持PDF和Word两种格式导出
- ✅ 可选择导出内容：试卷题目、答案、解析（支持任意组合）
- ✅ 丰富的格式选项：页面大小、字体大小、页边距等
- ✅ 同步导出：适合小文件，立即下载
- ✅ 异步导出：适合大文件，后台生成完成后下载
- ✅ 实时进度显示和状态查询
- ✅ 支持图片内容的处理和插入

## API接口

### 1. 同步导出PDF
```
POST /qh/paper/export/pdf
Content-Type: application/json

{
  "paperId": "试卷ID",
  "exportType": "PDF",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": false,
    "includeAnalysis": false
  },
  "formatOptions": {
    "pageSize": "A4",
    "fontSize": 12,
    "showQuestionNumbers": true,
    "showScores": true,
    "margin": 20,
    "lineSpacing": 1.5
  },
  "flag": "ZJ"
}
```

### 2. 同步导出Word
```
POST /qh/paper/export/word
Content-Type: application/json

{
  "paperId": "试卷ID",
  "exportType": "WORD",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": true,
    "includeAnalysis": true
  },
  "formatOptions": {
    "pageSize": "A4",
    "fontSize": 12,
    "showQuestionNumbers": true,
    "showScores": true,
    "margin": 20,
    "lineSpacing": 1.5
  },
  "flag": "ZJ"
}
```

### 3. 异步导出
```
POST /qh/paper/export/async
Content-Type: application/json

{
  "paperId": "试卷ID",
  "exportType": "PDF",
  "contentOptions": {
    "includeQuestions": true,
    "includeAnswers": true,
    "includeAnalysis": false
  },
  "formatOptions": {
    "pageSize": "A4",
    "fontSize": 14,
    "showQuestionNumbers": true,
    "showScores": true,
    "margin": 25,
    "lineSpacing": 1.8
  },
  "flag": "ZJ"
}

响应：
{
  "code": 200,
  "data": "task-uuid-12345",
  "msg": "success"
}
```

### 4. 查询导出状态
```
GET /qh/paper/export/status/{taskId}

响应：
{
  "code": 200,
  "data": {
    "status": "processing",  // processing, completed, failed
    "progress": 60,
    "message": "生成文档...",
    "createTime": 1691234567890
  }
}
```

### 5. 下载导出文件
```
GET /qh/paper/export/download/{taskId}
```

## 参数说明

### QhPaperExportDTO

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| paperId | String | 是 | 试卷ID |
| exportType | String | 是 | 导出类型：PDF、WORD |
| contentOptions | ContentOptions | 是 | 内容选项 |
| formatOptions | FormatOptions | 否 | 格式选项 |
| flag | String | 否 | 试卷标识：ZJ-组卷，其他-原生试卷 |

### ContentOptions

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| includeQuestions | Boolean | true | 是否包含试卷题目 |
| includeAnswers | Boolean | false | 是否包含答案 |
| includeAnalysis | Boolean | false | 是否包含解析 |

### FormatOptions

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| pageSize | String | A4 | 页面大小：A4、A3、Letter |
| fontSize | Integer | 12 | 字体大小 |
| showQuestionNumbers | Boolean | true | 是否显示题目序号 |
| showScores | Boolean | true | 是否显示分数 |
| margin | Integer | 20 | 页边距（毫米） |
| lineSpacing | Double | 1.5 | 行间距 |

## 前端使用示例

### 1. JavaScript原生调用

```javascript
// 同步导出PDF
exportPaperToPdf('paper123', {
    includeQuestions: true,
    includeAnswers: false,
    includeAnalysis: false,
    fontSize: 14
});

// 异步导出Word
exportPaperAsync('paper123', 'WORD', {
    includeQuestions: true,
    includeAnswers: true,
    includeAnalysis: true
}).then(taskId => {
    // 轮询状态并自动下载
    pollExportStatusAndDownload(taskId, (status) => {
        console.log('进度:', status.progress + '%');
    });
});
```

### 2. Vue组件使用

```vue
<template>
  <div>
    <el-button @click="showExportDialog = true">导出试卷</el-button>
    
    <PaperExportDialog
      :visible.sync="showExportDialog"
      :paper-id="currentPaperId"
      :paper-flag="currentPaperFlag"
    />
  </div>
</template>

<script>
import PaperExportDialog from './PaperExportDialog.vue';

export default {
  components: {
    PaperExportDialog
  },
  data() {
    return {
      showExportDialog: false,
      currentPaperId: 'paper123',
      currentPaperFlag: 'ZJ'
    };
  }
};
</script>
```

## 配置说明

### 应用配置

在 `application.yml` 中添加配置：

```yaml
paper:
  export:
    path: ./exports  # 异步导出文件存储路径
```

### 权限配置

需要配置以下权限：
- `qh:paper:export` - 试卷导出权限

## 技术实现

### 后端技术栈
- Spring Boot + Aspose Words
- MinIO文件存储
- Redis状态管理
- 异步任务处理

### 核心流程
1. 接收导出请求参数
2. 获取试卷和题目数据
3. 从MinIO下载图片资源
4. 使用Aspose Words生成文档
5. 转换为目标格式并输出

### 异步处理流程
1. 提交导出任务，返回任务ID
2. 后台异步生成文档
3. Redis存储任务状态和进度
4. 前端轮询查询状态
5. 生成完成后提供下载链接

## 注意事项

1. **文件大小限制**：建议大文件使用异步导出
2. **图片处理**：确保MinIO中的图片可正常访问
3. **内存使用**：大量图片可能占用较多内存
4. **任务清理**：异步任务完成后会自动清理临时文件
5. **权限控制**：确保用户有相应的导出权限

## 故障排查

### 常见问题

1. **导出失败**
   - 检查试卷ID是否存在
   - 检查MinIO连接是否正常
   - 查看后端日志错误信息

2. **图片显示异常**
   - 检查图片URL是否可访问
   - 确认网络连接正常
   - 查看Aspose Words许可证是否有效

3. **异步任务超时**
   - 检查Redis连接
   - 确认异步线程池配置
   - 查看任务执行日志

### 日志查看

```bash
# 查看导出相关日志
tail -f logs/domino_log.log | grep "PaperExport"
```

## 扩展功能

后续可以考虑添加的功能：
- 批量导出多个试卷
- 自定义模板支持
- 水印添加
- 导出历史记录
- 邮件发送导出文件
